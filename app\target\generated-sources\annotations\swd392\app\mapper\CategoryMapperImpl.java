package swd392.app.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import swd392.app.dto.request.CategoryRequest;
import swd392.app.dto.response.CategoryResponse;
import swd392.app.entity.Category;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class CategoryMapperImpl implements CategoryMapper {

    @Override
    public Category toEntity(CategoryRequest request) {
        if ( request == null ) {
            return null;
        }

        Category.CategoryBuilder category = Category.builder();

        category.categoryCode( request.getCategoryCode() );
        category.categoryName( request.getCategoryName() );

        return category.build();
    }

    @Override
    public CategoryResponse toResponse(Category category) {
        if ( category == null ) {
            return null;
        }

        CategoryResponse.CategoryResponseBuilder categoryResponse = CategoryResponse.builder();

        categoryResponse.categoryCode( category.getCategoryCode() );
        categoryResponse.categoryName( category.getCategoryName() );

        return categoryResponse.build();
    }
}
