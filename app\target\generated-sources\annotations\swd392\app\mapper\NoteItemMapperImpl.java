package swd392.app.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import swd392.app.dto.response.NoteItemResponse;
import swd392.app.entity.NoteItem;
import swd392.app.entity.Product;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NoteItemMapperImpl implements NoteItemMapper {

    @Override
    public NoteItemResponse toResponse(NoteItem noteItem) {
        if ( noteItem == null ) {
            return null;
        }

        NoteItemResponse.NoteItemResponseBuilder noteItemResponse = NoteItemResponse.builder();

        noteItemResponse.noteItemCode( noteItem.getNoteItemCode() );
        noteItemResponse.productCode( noteItemProductProductCode( noteItem ) );
        noteItemResponse.quantity( noteItem.getQuantity() );

        return noteItemResponse.build();
    }

    private String noteItemProductProductCode(NoteItem noteItem) {
        if ( noteItem == null ) {
            return null;
        }
        Product product = noteItem.getProduct();
        if ( product == null ) {
            return null;
        }
        String productCode = product.getProductCode();
        if ( productCode == null ) {
            return null;
        }
        return productCode;
    }
}
