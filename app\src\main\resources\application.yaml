server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
#      url: "***********************************************"
#      username: root
#      password: root
    url: *******************************************************************************************************************************************
    username: duongtb
    password: 17122004Admin
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true

openapi:
  service:
    api-docs: api-service
    server: http://localhost:8080/api
    title: API Service
    version: 1.0.0
jwt:
  signerKey: "eaRge+NAiFb7HQITA/QcCaDmS7QXJlwy7UpOAJj5/ddqoWCYQquoPXkget8OK+zA"