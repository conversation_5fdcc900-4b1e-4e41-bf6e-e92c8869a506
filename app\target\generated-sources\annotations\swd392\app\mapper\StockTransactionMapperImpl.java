package swd392.app.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import swd392.app.dto.response.StockExchangeResponse;
import swd392.app.entity.ExchangeNote;
import swd392.app.entity.User;
import swd392.app.entity.Warehouse;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class StockTransactionMapperImpl implements StockTransactionMapper {

    @Override
    public StockExchangeResponse toResponse(ExchangeNote transaction) {
        if ( transaction == null ) {
            return null;
        }

        StockExchangeResponse stockExchangeResponse = new StockExchangeResponse();

        stockExchangeResponse.setTransactionId( transaction.getExchangeNoteId() );
        stockExchangeResponse.setTransactionType( transaction.getTransactionType() );
        stockExchangeResponse.setSourceWarehouseCode( transactionSourceWarehouseWarehouseCode( transaction ) );
        stockExchangeResponse.setDestinationWarehouseCode( transactionDestinationWarehouseWarehouseCode( transaction ) );
        stockExchangeResponse.setCreatedBy( transactionCreatedByUserCode( transaction ) );
        stockExchangeResponse.setApprovedBy( transactionApprovedByUserCode( transaction ) );
        stockExchangeResponse.setStatus( transaction.getStatus() );
        stockExchangeResponse.setItems( mapNoteItems( transaction.getNoteItems() ) );

        return stockExchangeResponse;
    }

    private String transactionSourceWarehouseWarehouseCode(ExchangeNote exchangeNote) {
        if ( exchangeNote == null ) {
            return null;
        }
        Warehouse sourceWarehouse = exchangeNote.getSourceWarehouse();
        if ( sourceWarehouse == null ) {
            return null;
        }
        String warehouseCode = sourceWarehouse.getWarehouseCode();
        if ( warehouseCode == null ) {
            return null;
        }
        return warehouseCode;
    }

    private String transactionDestinationWarehouseWarehouseCode(ExchangeNote exchangeNote) {
        if ( exchangeNote == null ) {
            return null;
        }
        Warehouse destinationWarehouse = exchangeNote.getDestinationWarehouse();
        if ( destinationWarehouse == null ) {
            return null;
        }
        String warehouseCode = destinationWarehouse.getWarehouseCode();
        if ( warehouseCode == null ) {
            return null;
        }
        return warehouseCode;
    }

    private String transactionCreatedByUserCode(ExchangeNote exchangeNote) {
        if ( exchangeNote == null ) {
            return null;
        }
        User createdBy = exchangeNote.getCreatedBy();
        if ( createdBy == null ) {
            return null;
        }
        String userCode = createdBy.getUserCode();
        if ( userCode == null ) {
            return null;
        }
        return userCode;
    }

    private String transactionApprovedByUserCode(ExchangeNote exchangeNote) {
        if ( exchangeNote == null ) {
            return null;
        }
        User approvedBy = exchangeNote.getApprovedBy();
        if ( approvedBy == null ) {
            return null;
        }
        String userCode = approvedBy.getUserCode();
        if ( userCode == null ) {
            return null;
        }
        return userCode;
    }
}
