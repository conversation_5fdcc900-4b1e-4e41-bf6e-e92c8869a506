package swd392.app.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import swd392.app.dto.request.UserCreationRequest;
import swd392.app.dto.request.UserUpdateRequest;
import swd392.app.dto.response.UserResponse;
import swd392.app.entity.User;
import swd392.app.entity.Warehouse;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class UserMapperImpl implements UserMapper {

    @Override
    public User toUser(UserCreationRequest request) {
        if ( request == null ) {
            return null;
        }

        User.UserBuilder user = User.builder();

        user.userCode( request.getUserCode() );
        user.userName( request.getUserName() );
        user.fullName( request.getFullName() );
        user.email( request.getEmail() );
        user.password( request.getPassword() );
        user.status( request.getStatus() );

        return user.build();
    }

    @Override
    public UserResponse toUserResponse(User user) {
        if ( user == null ) {
            return null;
        }

        UserResponse.UserResponseBuilder userResponse = UserResponse.builder();

        userResponse.id( user.getUserId() );
        userResponse.userCode( user.getUserCode() );
        userResponse.userName( user.getUserName() );
        userResponse.fullName( user.getFullName() );
        userResponse.warehouseCode( userWarehouseWarehouseCode( user ) );
        userResponse.email( user.getEmail() );
        userResponse.role( user.getRole() );

        return userResponse.build();
    }

    @Override
    public void updateUser(User user, UserUpdateRequest request) {
        if ( request == null ) {
            return;
        }

        user.setUserCode( request.getUserCode() );
        user.setUserName( request.getUserName() );
        user.setFullName( request.getFullName() );
        user.setEmail( request.getEmail() );
        user.setPassword( request.getPassword() );
    }

    private String userWarehouseWarehouseCode(User user) {
        if ( user == null ) {
            return null;
        }
        Warehouse warehouse = user.getWarehouse();
        if ( warehouse == null ) {
            return null;
        }
        String warehouseCode = warehouse.getWarehouseCode();
        if ( warehouseCode == null ) {
            return null;
        }
        return warehouseCode;
    }
}
