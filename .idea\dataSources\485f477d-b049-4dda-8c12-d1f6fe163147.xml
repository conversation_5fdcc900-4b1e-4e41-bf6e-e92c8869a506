<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="<EMAIL>">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||azure_superuser|127.0.0.1|ALTER|G
|root||azure_superuser|localhost|ALTER|G
|root||duongtb||ALTER|G
|root||azure_superuser|127.0.0.1|ALTER ROUTINE|G
|root||azure_superuser|localhost|ALTER ROUTINE|G
|root||duongtb||ALTER ROUTINE|G
|root||azure_superuser|127.0.0.1|APPLICATION_PASSWORD_ADMIN|G
|root||azure_superuser|localhost|APPLICATION_PASSWORD_ADMIN|G
|root||duongtb||APPLICATION_PASSWORD_ADMIN|G
|root||azure_superuser|127.0.0.1|AUDIT_ABORT_EXEMPT|G
|root||azure_superuser|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.infoschema|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.session|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.sys|localhost|AUDIT_ABORT_EXEMPT|G
|root||azure_superuser|127.0.0.1|AUDIT_ADMIN|G
|root||azure_superuser|localhost|AUDIT_ADMIN|G
|root||azure_superuser|127.0.0.1|AUTHENTICATION_POLICY_ADMIN|G
|root||azure_superuser|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||mysql.session|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||azure_superuser|127.0.0.1|BACKUP_ADMIN|G
|root||azure_superuser|localhost|BACKUP_ADMIN|G
|root||mysql.session|localhost|BACKUP_ADMIN|G
|root||azure_superuser|127.0.0.1|BINLOG_ADMIN|G
|root||azure_superuser|localhost|BINLOG_ADMIN|G
|root||azure_superuser|127.0.0.1|BINLOG_ENCRYPTION_ADMIN|G
|root||azure_superuser|localhost|BINLOG_ENCRYPTION_ADMIN|G
|root||azure_superuser|127.0.0.1|CLONE_ADMIN|G
|root||azure_superuser|localhost|CLONE_ADMIN|G
|root||mysql.session|localhost|CLONE_ADMIN|G
|root||azure_superuser|127.0.0.1|CONNECTION_ADMIN|G
|root||azure_superuser|localhost|CONNECTION_ADMIN|G
|root||mysql.session|localhost|CONNECTION_ADMIN|G
|root||azure_superuser|127.0.0.1|CREATE|G
|root||azure_superuser|localhost|CREATE|G
|root||duongtb||CREATE|G
|root||azure_superuser|127.0.0.1|CREATE ROLE|G
|root||azure_superuser|localhost|CREATE ROLE|G
|root||duongtb||CREATE ROLE|G
|root||azure_superuser|127.0.0.1|CREATE ROUTINE|G
|root||azure_superuser|localhost|CREATE ROUTINE|G
|root||duongtb||CREATE ROUTINE|G
|root||azure_superuser|127.0.0.1|CREATE TABLESPACE|G
|root||azure_superuser|localhost|CREATE TABLESPACE|G
|root||azure_superuser|127.0.0.1|CREATE TEMPORARY TABLES|G
|root||azure_superuser|localhost|CREATE TEMPORARY TABLES|G
|root||duongtb||CREATE TEMPORARY TABLES|G
|root||azure_superuser|127.0.0.1|CREATE USER|G
|root||azure_superuser|localhost|CREATE USER|G
|root||duongtb||CREATE USER|G
|root||azure_superuser|127.0.0.1|CREATE VIEW|G
|root||azure_superuser|localhost|CREATE VIEW|G
|root||duongtb||CREATE VIEW|G
|root||azure_superuser|127.0.0.1|DELETE|G
|root||azure_superuser|localhost|DELETE|G
|root||duongtb||DELETE|G
|root||azure_superuser|127.0.0.1|DROP|G
|root||azure_superuser|localhost|DROP|G
|root||duongtb||DROP|G
|root||azure_superuser|127.0.0.1|DROP ROLE|G
|root||azure_superuser|localhost|DROP ROLE|G
|root||duongtb||DROP ROLE|G
|root||azure_superuser|127.0.0.1|ENCRYPTION_KEY_ADMIN|G
|root||azure_superuser|localhost|ENCRYPTION_KEY_ADMIN|G
|root||azure_superuser|127.0.0.1|EVENT|G
|root||azure_superuser|localhost|EVENT|G
|root||duongtb||EVENT|G
|root||azure_superuser|127.0.0.1|EXECUTE|G
|root||azure_superuser|localhost|EXECUTE|G
|root||duongtb||EXECUTE|G
|root||azure_superuser|127.0.0.1|FILE|G
|root||azure_superuser|localhost|FILE|G
|root||azure_superuser|127.0.0.1|FIREWALL_EXEMPT|G
|root||azure_superuser|localhost|FIREWALL_EXEMPT|G
|root||mysql.infoschema|localhost|FIREWALL_EXEMPT|G
|root||mysql.session|localhost|FIREWALL_EXEMPT|G
|root||mysql.sys|localhost|FIREWALL_EXEMPT|G
|root||azure_superuser|127.0.0.1|FLUSH_OPTIMIZER_COSTS|G
|root||azure_superuser|localhost|FLUSH_OPTIMIZER_COSTS|G
|root||duongtb||FLUSH_OPTIMIZER_COSTS|G
|root||azure_superuser|127.0.0.1|FLUSH_STATUS|G
|root||azure_superuser|localhost|FLUSH_STATUS|G
|root||duongtb||FLUSH_STATUS|G
|root||azure_superuser|127.0.0.1|FLUSH_TABLES|G
|root||azure_superuser|localhost|FLUSH_TABLES|G
|root||duongtb||FLUSH_TABLES|G
|root||azure_superuser|127.0.0.1|FLUSH_USER_RESOURCES|G
|root||azure_superuser|localhost|FLUSH_USER_RESOURCES|G
|root||duongtb||FLUSH_USER_RESOURCES|G
|root||azure_superuser|127.0.0.1|GROUP_REPLICATION_ADMIN|G
|root||azure_superuser|localhost|GROUP_REPLICATION_ADMIN|G
|root||azure_superuser|127.0.0.1|GROUP_REPLICATION_STREAM|G
|root||azure_superuser|localhost|GROUP_REPLICATION_STREAM|G
|root||azure_superuser|127.0.0.1|INDEX|G
|root||azure_superuser|localhost|INDEX|G
|root||duongtb||INDEX|G
|root||azure_superuser|127.0.0.1|INNODB_REDO_LOG_ARCHIVE|G
|root||azure_superuser|localhost|INNODB_REDO_LOG_ARCHIVE|G
|root||azure_superuser|127.0.0.1|INNODB_REDO_LOG_ENABLE|G
|root||azure_superuser|localhost|INNODB_REDO_LOG_ENABLE|G
|root||azure_superuser|127.0.0.1|INSERT|G
|root||azure_superuser|localhost|INSERT|G
|root||duongtb||INSERT|G
|root||azure_superuser|127.0.0.1|LOCK TABLES|G
|root||azure_superuser|localhost|LOCK TABLES|G
|root||duongtb||LOCK TABLES|G
|root||azure_superuser|127.0.0.1|PASSWORDLESS_USER_ADMIN|G
|root||azure_superuser|localhost|PASSWORDLESS_USER_ADMIN|G
|root||azure_superuser|127.0.0.1|PERSIST_RO_VARIABLES_ADMIN|G
|root||azure_superuser|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||mysql.session|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||azure_superuser|127.0.0.1|PROCESS|G
|root||azure_superuser|localhost|PROCESS|G
|root||duongtb||PROCESS|G
|root||azure_superuser|127.0.0.1|REFERENCES|G
|root||azure_superuser|localhost|REFERENCES|G
|root||duongtb||REFERENCES|G
|root||azure_superuser|127.0.0.1|RELOAD|G
|root||azure_superuser|localhost|RELOAD|G
|root||duongtb||RELOAD|G
|root||azure_superuser|127.0.0.1|REPLICATION CLIENT|G
|root||azure_superuser|localhost|REPLICATION CLIENT|G
|root||duongtb||REPLICATION CLIENT|G
|root||azure_superuser|127.0.0.1|REPLICATION SLAVE|G
|root||azure_superuser|localhost|REPLICATION SLAVE|G
|root||duongtb||REPLICATION SLAVE|G
|root||azure_superuser|127.0.0.1|REPLICATION_APPLIER|G
|root||azure_superuser|localhost|REPLICATION_APPLIER|G
|root||duongtb||REPLICATION_APPLIER|G
|root||azure_superuser|127.0.0.1|REPLICATION_SLAVE_ADMIN|G
|root||azure_superuser|localhost|REPLICATION_SLAVE_ADMIN|G
|root||azure_superuser|127.0.0.1|RESOURCE_GROUP_ADMIN|G
|root||azure_superuser|localhost|RESOURCE_GROUP_ADMIN|G
|root||azure_superuser|127.0.0.1|RESOURCE_GROUP_USER|G
|root||azure_superuser|localhost|RESOURCE_GROUP_USER|G
|root||azure_superuser|127.0.0.1|ROLE_ADMIN|G
|root||azure_superuser|localhost|ROLE_ADMIN|G
|root||duongtb||ROLE_ADMIN|G
|root||azure_superuser|127.0.0.1|SELECT|G
|root||azure_superuser|localhost|SELECT|G
|root||duongtb||SELECT|G
|root||mysql.infoschema|localhost|SELECT|G
|root||azure_superuser|127.0.0.1|SENSITIVE_VARIABLES_OBSERVER|G
|root||azure_superuser|localhost|SENSITIVE_VARIABLES_OBSERVER|G
|root||azure_superuser|127.0.0.1|SERVICE_CONNECTION_ADMIN|G
|root||azure_superuser|localhost|SERVICE_CONNECTION_ADMIN|G
|root||azure_superuser|127.0.0.1|SESSION_VARIABLES_ADMIN|G
|root||azure_superuser|localhost|SESSION_VARIABLES_ADMIN|G
|root||duongtb||SESSION_VARIABLES_ADMIN|G
|root||mysql.session|localhost|SESSION_VARIABLES_ADMIN|G
|root||azure_superuser|127.0.0.1|SET_USER_ID|G
|root||azure_superuser|localhost|SET_USER_ID|G
|root||duongtb||SET_USER_ID|G
|root||azure_superuser|127.0.0.1|SHOW DATABASES|G
|root||azure_superuser|localhost|SHOW DATABASES|G
|root||duongtb||SHOW DATABASES|G
|root||azure_superuser|127.0.0.1|SHOW VIEW|G
|root||azure_superuser|localhost|SHOW VIEW|G
|root||duongtb||SHOW VIEW|G
|root||azure_superuser|127.0.0.1|SHOW_ROUTINE|G
|root||azure_superuser|localhost|SHOW_ROUTINE|G
|root||duongtb||SHOW_ROUTINE|G
|root||azure_superuser|127.0.0.1|SHUTDOWN|G
|root||azure_superuser|localhost|SHUTDOWN|G
|root||mysql.session|localhost|SHUTDOWN|G
|root||azure_superuser|127.0.0.1|SUPER|G
|root||azure_superuser|localhost|SUPER|G
|root||mysql.session|localhost|SUPER|G
|root||azure_superuser|127.0.0.1|SYSTEM_USER|G
|root||azure_superuser|localhost|SYSTEM_USER|G
|root||mysql.infoschema|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_USER|G
|root||mysql.sys|localhost|SYSTEM_USER|G
|root||azure_superuser|127.0.0.1|SYSTEM_VARIABLES_ADMIN|G
|root||azure_superuser|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||mysql.session|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||azure_superuser|127.0.0.1|TABLE_ENCRYPTION_ADMIN|G
|root||azure_superuser|localhost|TABLE_ENCRYPTION_ADMIN|G
|root||azure_superuser|127.0.0.1|TELEMETRY_LOG_ADMIN|G
|root||azure_superuser|localhost|TELEMETRY_LOG_ADMIN|G
|root||azure_superuser|127.0.0.1|TRIGGER|G
|root||azure_superuser|localhost|TRIGGER|G
|root||duongtb||TRIGGER|G
|root||azure_superuser|127.0.0.1|UPDATE|G
|root||azure_superuser|localhost|UPDATE|G
|root||duongtb||UPDATE|G
|root||azure_superuser|127.0.0.1|XA_RECOVER_ADMIN|G
|root||azure_superuser|localhost|XA_RECOVER_ADMIN|G
|root||duongtb||XA_RECOVER_ADMIN|G
|root||azure_superuser|127.0.0.1|grant option|G
|root||azure_superuser|localhost|grant option|G
|root||duongtb||grant option|G
performance_schema|schema||duongtb||DELETE|G
performance_schema|schema||mysql.session|localhost|SELECT|G
performance_schema|schema||duongtb||UPDATE|G
performance_schema|schema||duongtb||grant option|G
sys|schema||mysql.sys|localhost|TRIGGER|G</Grants>
      <ServerVersion>8.0.41</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="inventorymanagement">
      <Current>1</Current>
      <LastIntrospectionLocalTimestamp>2025-07-03.05:28:46</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="mysql">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <user id="293" parent="1" name="azure_superuser">
      <Host>127.0.0.1</Host>
    </user>
    <user id="294" parent="1" name="azure_superuser">
      <Host>localhost</Host>
    </user>
    <user id="295" parent="1" name="duongtb"/>
    <user id="296" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="297" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="298" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <table id="299" parent="289" name="category">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="300" parent="289" name="exchange_note">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="301" parent="289" name="exchangenote">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="302" parent="289" name="invalidated_token">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="303" parent="289" name="invalidatedtoken">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="304" parent="289" name="note_item">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="305" parent="289" name="noteitem">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="306" parent="289" name="product">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="307" parent="289" name="product_type">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="308" parent="289" name="producttype">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="309" parent="289" name="role">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="310" parent="289" name="stock_check_note">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="311" parent="289" name="stock_check_product">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="312" parent="289" name="stockchecknote">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="313" parent="289" name="stockcheckproduct">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="314" parent="289" name="user">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="315" parent="289" name="warehouse">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <column id="316" parent="299" name="category_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="317" parent="299" name="category_code">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="318" parent="299" name="category_name">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="319" parent="299" name="PRIMARY">
      <ColNames>category_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="320" parent="299" name="category_code">
      <ColNames>category_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="321" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="322" parent="299" name="category_code">
      <UnderlyingIndexName>category_code</UnderlyingIndexName>
    </key>
    <column id="323" parent="300" name="exchange_note_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="324" parent="300" name="date">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime(6)|0s</StoredType>
    </column>
    <column id="325" parent="300" name="status">
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>enum(&apos;pending&apos;, &apos;accepted&apos;, &apos;finished&apos;, &apos;rejected&apos;)|0e</StoredType>
    </column>
    <column id="326" parent="300" name="transaction_type">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>enum(&apos;IMPORT&apos;, &apos;EXPORT&apos;)|0e</StoredType>
    </column>
    <column id="327" parent="300" name="approved_by">
      <Position>5</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="328" parent="300" name="created_by">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="329" parent="300" name="destination_warehouse_code">
      <Position>7</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="330" parent="300" name="source_warehouse_code">
      <Position>8</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <foreign-key id="331" parent="300" name="FKjxqhbb4d1ogafq5021kaf3aj4">
      <ColNames>approved_by</ColNames>
      <RefColNames>user_code</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="332" parent="300" name="FKf3ir7puh5j4vp3q3958vs777v">
      <ColNames>created_by</ColNames>
      <RefColNames>user_code</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="333" parent="300" name="FK1dqdm83kc2apav6tiapdyl09f">
      <ColNames>destination_warehouse_code</ColNames>
      <RefColNames>warehouse_code</RefColNames>
      <RefTableName>warehouse</RefTableName>
    </foreign-key>
    <foreign-key id="334" parent="300" name="FKq3ckrx65qaqtdnmdfpc9c04x1">
      <ColNames>source_warehouse_code</ColNames>
      <RefColNames>warehouse_code</RefColNames>
      <RefTableName>warehouse</RefTableName>
    </foreign-key>
    <index id="335" parent="300" name="PRIMARY">
      <ColNames>exchange_note_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="336" parent="300" name="FKjxqhbb4d1ogafq5021kaf3aj4">
      <ColNames>approved_by</ColNames>
      <Type>btree</Type>
    </index>
    <index id="337" parent="300" name="FKf3ir7puh5j4vp3q3958vs777v">
      <ColNames>created_by</ColNames>
      <Type>btree</Type>
    </index>
    <index id="338" parent="300" name="FK1dqdm83kc2apav6tiapdyl09f">
      <ColNames>destination_warehouse_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="339" parent="300" name="FKq3ckrx65qaqtdnmdfpc9c04x1">
      <ColNames>source_warehouse_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="340" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="341" parent="301" name="exchangeNote_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="342" parent="301" name="warehouse_code">
      <Position>2</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="343" parent="301" name="transactionType">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>enum(&apos;IMPORT&apos;, &apos;EXPORT&apos;)|0e</StoredType>
    </column>
    <column id="344" parent="301" name="status">
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>enum(&apos;pending&apos;, &apos;accepted&apos;, &apos;finished&apos;, &apos;rejected&apos;)|0e</StoredType>
    </column>
    <column id="345" parent="301" name="source_warehouse_id">
      <Position>5</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="346" parent="301" name="destination_warehouse_id">
      <Position>6</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="347" parent="301" name="created_by">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="348" parent="301" name="approved_by">
      <Position>8</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="349" parent="301" name="date">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="350" parent="301" name="destination_warehouse_code">
      <Position>10</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="351" parent="301" name="source_warehouse_code">
      <Position>11</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <foreign-key id="352" parent="301" name="exchangenote_ibfk_1">
      <ColNames>source_warehouse_id</ColNames>
      <RefColNames>warehouse_code</RefColNames>
      <RefTableName>warehouse</RefTableName>
    </foreign-key>
    <foreign-key id="353" parent="301" name="exchangenote_ibfk_2">
      <ColNames>destination_warehouse_id</ColNames>
      <RefColNames>warehouse_code</RefColNames>
      <RefTableName>warehouse</RefTableName>
    </foreign-key>
    <foreign-key id="354" parent="301" name="exchangenote_ibfk_3">
      <ColNames>created_by</ColNames>
      <RefColNames>user_code</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="355" parent="301" name="exchangenote_ibfk_4">
      <ColNames>approved_by</ColNames>
      <RefColNames>user_code</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="356" parent="301" name="FKe7p2gklxgjesvbrta9hyb617r">
      <ColNames>destination_warehouse_code</ColNames>
      <RefColNames>warehouse_code</RefColNames>
      <RefTableName>warehouse</RefTableName>
    </foreign-key>
    <foreign-key id="357" parent="301" name="FKeblq9j2frqtv3rpqchynf9mh7">
      <ColNames>source_warehouse_code</ColNames>
      <RefColNames>warehouse_code</RefColNames>
      <RefTableName>warehouse</RefTableName>
    </foreign-key>
    <index id="358" parent="301" name="PRIMARY">
      <ColNames>exchangeNote_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="359" parent="301" name="source_warehouse_id">
      <ColNames>source_warehouse_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="360" parent="301" name="destination_warehouse_id">
      <ColNames>destination_warehouse_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="361" parent="301" name="created_by">
      <ColNames>created_by</ColNames>
      <Type>btree</Type>
    </index>
    <index id="362" parent="301" name="approved_by">
      <ColNames>approved_by</ColNames>
      <Type>btree</Type>
    </index>
    <index id="363" parent="301" name="FKe7p2gklxgjesvbrta9hyb617r">
      <ColNames>destination_warehouse_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="364" parent="301" name="FKeblq9j2frqtv3rpqchynf9mh7">
      <ColNames>source_warehouse_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="365" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="366" parent="302" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="367" parent="302" name="expiry_time">
      <Position>2</Position>
      <StoredType>datetime(6)|0s</StoredType>
    </column>
    <index id="368" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="369" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="370" parent="303" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="371" parent="303" name="expiryTime">
      <Position>2</Position>
      <StoredType>datetime(6)|0s</StoredType>
    </column>
    <index id="372" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="373" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="374" parent="304" name="note_item_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="375" parent="304" name="note_item_code">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="376" parent="304" name="quantity">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="377" parent="304" name="status">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>enum(&apos;ACTIVE&apos;, &apos;CANCELED&apos;, &apos;COMPLETED&apos;)|0e</StoredType>
    </column>
    <column id="378" parent="304" name="exchange_note_id">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="379" parent="304" name="product_code">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <foreign-key id="380" parent="304" name="FKkt29evba7wqia2f9fk9s9mado">
      <ColNames>exchange_note_id</ColNames>
      <RefColNames>exchange_note_id</RefColNames>
      <RefTableName>exchange_note</RefTableName>
    </foreign-key>
    <foreign-key id="381" parent="304" name="FK6uknw47nj8kwqe7i1rn209vba">
      <ColNames>product_code</ColNames>
      <RefColNames>product_code</RefColNames>
      <RefTableName>product</RefTableName>
    </foreign-key>
    <index id="382" parent="304" name="PRIMARY">
      <ColNames>note_item_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="383" parent="304" name="UKk1lsrqwrnh9nw5h2fi7ug4r8a">
      <ColNames>note_item_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="384" parent="304" name="FKkt29evba7wqia2f9fk9s9mado">
      <ColNames>exchange_note_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="385" parent="304" name="FK6uknw47nj8kwqe7i1rn209vba">
      <ColNames>product_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="386" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="387" parent="304" name="UKk1lsrqwrnh9nw5h2fi7ug4r8a">
      <UnderlyingIndexName>UKk1lsrqwrnh9nw5h2fi7ug4r8a</UnderlyingIndexName>
    </key>
    <column id="388" parent="305" name="noteItem_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="389" parent="305" name="noteItem_code">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="390" parent="305" name="product_code">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="391" parent="305" name="exchangeNote_id">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="392" parent="305" name="quantity">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="393" parent="305" name="status">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>enum(&apos;ACTIVE&apos;, &apos;CANCELED&apos;, &apos;COMPLETED&apos;)|0e</StoredType>
    </column>
    <foreign-key id="394" parent="305" name="noteitem_ibfk_1">
      <ColNames>product_code</ColNames>
      <RefColNames>product_code</RefColNames>
      <RefTableName>product</RefTableName>
    </foreign-key>
    <foreign-key id="395" parent="305" name="noteitem_ibfk_2">
      <ColNames>exchangeNote_id</ColNames>
      <RefColNames>exchangeNote_id</RefColNames>
      <RefTableName>exchangenote</RefTableName>
    </foreign-key>
    <index id="396" parent="305" name="PRIMARY">
      <ColNames>noteItem_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="397" parent="305" name="noteItem_code">
      <ColNames>noteItem_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="398" parent="305" name="product_code">
      <ColNames>product_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="399" parent="305" name="exchangeNote_id">
      <ColNames>exchangeNote_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="400" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="401" parent="305" name="noteItem_code">
      <UnderlyingIndexName>noteItem_code</UnderlyingIndexName>
    </key>
    <column id="402" parent="306" name="product_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="403" parent="306" name="product_code">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="404" parent="306" name="product_name">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="405" parent="306" name="size">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="406" parent="306" name="color">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="407" parent="306" name="quantity">
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="408" parent="306" name="status">
      <DefaultExpression>&apos;instock&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>enum(&apos;instock&apos;, &apos;outofstock&apos;)|0e</StoredType>
    </column>
    <column id="409" parent="306" name="productType_code">
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="410" parent="306" name="updated_at">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="411" parent="306" name="product_type_code">
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <foreign-key id="412" parent="306" name="product_ibfk_1">
      <ColNames>productType_code</ColNames>
      <RefColNames>productType_code</RefColNames>
      <RefTableName>producttype</RefTableName>
    </foreign-key>
    <index id="413" parent="306" name="PRIMARY">
      <ColNames>product_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="414" parent="306" name="product_code">
      <ColNames>product_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="415" parent="306" name="productType_code">
      <ColNames>productType_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="416" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="417" parent="306" name="product_code">
      <UnderlyingIndexName>product_code</UnderlyingIndexName>
    </key>
    <column id="418" parent="307" name="product_type_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="419" parent="307" name="price">
      <Position>2</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="420" parent="307" name="product_type_code">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="421" parent="307" name="product_type_name">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="422" parent="307" name="category_code">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <foreign-key id="423" parent="307" name="FKpyrscwwk70brnuc2aunyw7nsu">
      <ColNames>category_code</ColNames>
      <RefColNames>category_code</RefColNames>
      <RefTableName>category</RefTableName>
    </foreign-key>
    <index id="424" parent="307" name="PRIMARY">
      <ColNames>product_type_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="425" parent="307" name="UKkafe6liyk82lh8pt0jlb81rsj">
      <ColNames>product_type_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="426" parent="307" name="FKpyrscwwk70brnuc2aunyw7nsu">
      <ColNames>category_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="427" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="428" parent="307" name="UKkafe6liyk82lh8pt0jlb81rsj">
      <UnderlyingIndexName>UKkafe6liyk82lh8pt0jlb81rsj</UnderlyingIndexName>
    </key>
    <column id="429" parent="308" name="productType_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="430" parent="308" name="productType_code">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="431" parent="308" name="productType_name">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="432" parent="308" name="price">
      <Position>4</Position>
      <StoredType>double|0s</StoredType>
    </column>
    <column id="433" parent="308" name="category_code">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <foreign-key id="434" parent="308" name="producttype_ibfk_1">
      <ColNames>category_code</ColNames>
      <RefColNames>category_code</RefColNames>
      <RefTableName>category</RefTableName>
    </foreign-key>
    <index id="435" parent="308" name="PRIMARY">
      <ColNames>productType_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="436" parent="308" name="productType_code">
      <ColNames>productType_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="437" parent="308" name="category_code">
      <ColNames>category_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="438" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="439" parent="308" name="productType_code">
      <UnderlyingIndexName>productType_code</UnderlyingIndexName>
    </key>
    <column id="440" parent="309" name="role_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="441" parent="309" name="role_type">
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="442" parent="309" name="role_name">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="443" parent="309" name="PRIMARY">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="444" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="445" parent="310" name="stock_check_note_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="446" parent="310" name="date_time">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime(6)|0s</StoredType>
    </column>
    <column id="447" parent="310" name="description">
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="448" parent="310" name="stock_check_status">
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>enum(&apos;pending&apos;, &apos;approved&apos;, &apos;finished&apos;, &apos;rejected&apos;)|0e</StoredType>
    </column>
    <column id="449" parent="310" name="checker">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="450" parent="310" name="warehouse_code">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <foreign-key id="451" parent="310" name="FKobbrbu9ordxydubb4bwej0xvf">
      <ColNames>checker</ColNames>
      <RefColNames>user_code</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="452" parent="310" name="FKoe9j46joqh9w6kxxs1emfsje8">
      <ColNames>warehouse_code</ColNames>
      <RefColNames>warehouse_code</RefColNames>
      <RefTableName>warehouse</RefTableName>
    </foreign-key>
    <index id="453" parent="310" name="PRIMARY">
      <ColNames>stock_check_note_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="454" parent="310" name="FKobbrbu9ordxydubb4bwej0xvf">
      <ColNames>checker</ColNames>
      <Type>btree</Type>
    </index>
    <index id="455" parent="310" name="FKoe9j46joqh9w6kxxs1emfsje8">
      <ColNames>warehouse_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="456" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="457" parent="311" name="stock_check_product_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="458" parent="311" name="actual_quantity">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="459" parent="311" name="expected_quantity">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="460" parent="311" name="last_quantity">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="461" parent="311" name="stock_check_product_status">
      <DefaultExpression>&apos;temporary&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>enum(&apos;temporary&apos;, &apos;finished&apos;)|0e</StoredType>
    </column>
    <column id="462" parent="311" name="total_export_quantity">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="463" parent="311" name="total_import_quantity">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="464" parent="311" name="product_code">
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="465" parent="311" name="stock_check_note_id">
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <foreign-key id="466" parent="311" name="FKgab72uc1dk2irhl0gu7lb105p">
      <ColNames>product_code</ColNames>
      <RefColNames>product_code</RefColNames>
      <RefTableName>product</RefTableName>
    </foreign-key>
    <foreign-key id="467" parent="311" name="FKnarxx1arrujqchb9vj9hny181">
      <ColNames>stock_check_note_id</ColNames>
      <RefColNames>stock_check_note_id</RefColNames>
      <RefTableName>stock_check_note</RefTableName>
    </foreign-key>
    <index id="468" parent="311" name="PRIMARY">
      <ColNames>stock_check_product_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="469" parent="311" name="FKgab72uc1dk2irhl0gu7lb105p">
      <ColNames>product_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="470" parent="311" name="FKnarxx1arrujqchb9vj9hny181">
      <ColNames>stock_check_note_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="471" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="472" parent="312" name="stockCheckNote_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="473" parent="312" name="date_time">
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="474" parent="312" name="warehouse_code">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="475" parent="312" name="checker">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="476" parent="312" name="description">
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="477" parent="312" name="stockCheck_status">
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <Position>6</Position>
      <StoredType>enum(&apos;pending&apos;, &apos;accepted&apos;, &apos;finished&apos;, &apos;rejected&apos;)|0e</StoredType>
    </column>
    <foreign-key id="478" parent="312" name="stockchecknote_ibfk_1">
      <ColNames>warehouse_code</ColNames>
      <RefColNames>warehouse_code</RefColNames>
      <RefTableName>warehouse</RefTableName>
    </foreign-key>
    <foreign-key id="479" parent="312" name="stockchecknote_ibfk_2">
      <ColNames>checker</ColNames>
      <RefColNames>user_code</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <index id="480" parent="312" name="PRIMARY">
      <ColNames>stockCheckNote_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="481" parent="312" name="warehouse_code">
      <ColNames>warehouse_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="482" parent="312" name="checker">
      <ColNames>checker</ColNames>
      <Type>btree</Type>
    </index>
    <key id="483" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="484" parent="313" name="stockCheckProduct_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="485" parent="313" name="stockCheckNote_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="486" parent="313" name="product_code">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="487" parent="313" name="last_quantity">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="488" parent="313" name="total_export_quantity">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="489" parent="313" name="total_import_quantity">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="490" parent="313" name="actual_quantity">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="491" parent="313" name="expected_quantity">
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="492" parent="313" name="difference">
      <ColumnKind>generated-stored</ColumnKind>
      <DefaultExpression>(`actual_quantity` - `expected_quantity`)</DefaultExpression>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="493" parent="313" name="stockCheckProduct_status">
      <DefaultExpression>&apos;temporary&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>enum(&apos;temporary&apos;, &apos;finished&apos;)|0e</StoredType>
    </column>
    <foreign-key id="494" parent="313" name="stockcheckproduct_ibfk_1">
      <ColNames>stockCheckNote_id</ColNames>
      <RefColNames>stockCheckNote_id</RefColNames>
      <RefTableName>stockchecknote</RefTableName>
    </foreign-key>
    <foreign-key id="495" parent="313" name="stockcheckproduct_ibfk_2">
      <ColNames>product_code</ColNames>
      <RefColNames>product_code</RefColNames>
      <RefTableName>product</RefTableName>
    </foreign-key>
    <index id="496" parent="313" name="PRIMARY">
      <ColNames>stockCheckProduct_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="497" parent="313" name="stockCheckNote_id">
      <ColNames>stockCheckNote_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="498" parent="313" name="product_code">
      <ColNames>product_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="499" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="500" parent="314" name="user_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="501" parent="314" name="user_code">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="502" parent="314" name="role_id">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="503" parent="314" name="user_name">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="504" parent="314" name="full_name">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="505" parent="314" name="email">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="506" parent="314" name="password">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="507" parent="314" name="warehouse_code">
      <Position>8</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="508" parent="314" name="status">
      <DefaultExpression>&apos;inactive&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>enum(&apos;active&apos;, &apos;inactive&apos;)|0e</StoredType>
    </column>
    <column id="509" parent="314" name="created_at">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>10</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="510" parent="314" name="updated_at">
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <foreign-key id="511" parent="314" name="user_ibfk_1">
      <ColNames>role_id</ColNames>
      <RefColNames>role_id</RefColNames>
      <RefTableName>role</RefTableName>
    </foreign-key>
    <foreign-key id="512" parent="314" name="user_ibfk_2">
      <ColNames>warehouse_code</ColNames>
      <RefColNames>warehouse_code</RefColNames>
      <RefTableName>warehouse</RefTableName>
    </foreign-key>
    <index id="513" parent="314" name="PRIMARY">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="514" parent="314" name="user_code">
      <ColNames>user_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="515" parent="314" name="role_id">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="516" parent="314" name="warehouse_code">
      <ColNames>warehouse_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="517" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="518" parent="314" name="user_code">
      <UnderlyingIndexName>user_code</UnderlyingIndexName>
    </key>
    <column id="519" parent="315" name="warehouse_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="520" parent="315" name="warehouse_code">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(6)|0s</StoredType>
    </column>
    <column id="521" parent="315" name="warehouse_name">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="522" parent="315" name="address">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="523" parent="315" name="PRIMARY">
      <ColNames>warehouse_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="524" parent="315" name="warehouse_code">
      <ColNames>warehouse_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="525" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="526" parent="315" name="warehouse_code">
      <UnderlyingIndexName>warehouse_code</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>