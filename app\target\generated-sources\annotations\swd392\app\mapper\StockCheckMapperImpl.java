package swd392.app.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import swd392.app.dto.response.StockCheckNoteResponse;
import swd392.app.dto.response.StockCheckProductResponse;
import swd392.app.entity.Product;
import swd392.app.entity.StockCheckNote;
import swd392.app.entity.StockCheckProduct;
import swd392.app.entity.User;
import swd392.app.entity.Warehouse;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class StockCheckMapperImpl implements StockCheckMapper {

    @Override
    public StockCheckNoteResponse toStockCheckNoteResponse(StockCheckNote stockCheckNote) {
        if ( stockCheckNote == null ) {
            return null;
        }

        StockCheckNoteResponse.StockCheckNoteResponseBuilder stockCheckNoteResponse = StockCheckNoteResponse.builder();

        stockCheckNoteResponse.stockCheckNoteId( stockCheckNote.getStockCheckNoteId() );
        stockCheckNoteResponse.dateTime( stockCheckNote.getDateTime() );
        stockCheckNoteResponse.warehouseCode( stockCheckNoteWarehouseWarehouseCode( stockCheckNote ) );
        stockCheckNoteResponse.warehouseName( stockCheckNoteWarehouseWarehouseName( stockCheckNote ) );
        stockCheckNoteResponse.checkerName( stockCheckNoteCheckerFullName( stockCheckNote ) );
        if ( stockCheckNote.getStockCheckStatus() != null ) {
            stockCheckNoteResponse.stockCheckStatus( stockCheckNote.getStockCheckStatus().name() );
        }
        stockCheckNoteResponse.stockCheckProducts( toStockCheckProductResponses( stockCheckNote.getStockCheckProducts() ) );

        return stockCheckNoteResponse.build();
    }

    @Override
    public StockCheckProductResponse toStockCheckProductResponse(StockCheckProduct stockCheckProduct) {
        if ( stockCheckProduct == null ) {
            return null;
        }

        StockCheckProductResponse.StockCheckProductResponseBuilder stockCheckProductResponse = StockCheckProductResponse.builder();

        stockCheckProductResponse.productCode( stockCheckProductProductProductCode( stockCheckProduct ) );
        stockCheckProductResponse.productName( stockCheckProductProductProductName( stockCheckProduct ) );
        stockCheckProductResponse.lastQuantity( stockCheckProduct.getLastQuantity() );
        stockCheckProductResponse.totalImportQuantity( stockCheckProduct.getTotalImportQuantity() );
        stockCheckProductResponse.totalExportQuantity( stockCheckProduct.getTotalExportQuantity() );
        stockCheckProductResponse.actualQuantity( stockCheckProduct.getActualQuantity() );
        stockCheckProductResponse.expectedQuantity( stockCheckProduct.getExpectedQuantity() );

        stockCheckProductResponse.difference( stockCheckProduct.getActualQuantity() - stockCheckProduct.getExpectedQuantity() );

        return stockCheckProductResponse.build();
    }

    private String stockCheckNoteWarehouseWarehouseCode(StockCheckNote stockCheckNote) {
        if ( stockCheckNote == null ) {
            return null;
        }
        Warehouse warehouse = stockCheckNote.getWarehouse();
        if ( warehouse == null ) {
            return null;
        }
        String warehouseCode = warehouse.getWarehouseCode();
        if ( warehouseCode == null ) {
            return null;
        }
        return warehouseCode;
    }

    private String stockCheckNoteWarehouseWarehouseName(StockCheckNote stockCheckNote) {
        if ( stockCheckNote == null ) {
            return null;
        }
        Warehouse warehouse = stockCheckNote.getWarehouse();
        if ( warehouse == null ) {
            return null;
        }
        String warehouseName = warehouse.getWarehouseName();
        if ( warehouseName == null ) {
            return null;
        }
        return warehouseName;
    }

    private String stockCheckNoteCheckerFullName(StockCheckNote stockCheckNote) {
        if ( stockCheckNote == null ) {
            return null;
        }
        User checker = stockCheckNote.getChecker();
        if ( checker == null ) {
            return null;
        }
        String fullName = checker.getFullName();
        if ( fullName == null ) {
            return null;
        }
        return fullName;
    }

    private String stockCheckProductProductProductCode(StockCheckProduct stockCheckProduct) {
        if ( stockCheckProduct == null ) {
            return null;
        }
        Product product = stockCheckProduct.getProduct();
        if ( product == null ) {
            return null;
        }
        String productCode = product.getProductCode();
        if ( productCode == null ) {
            return null;
        }
        return productCode;
    }

    private String stockCheckProductProductProductName(StockCheckProduct stockCheckProduct) {
        if ( stockCheckProduct == null ) {
            return null;
        }
        Product product = stockCheckProduct.getProduct();
        if ( product == null ) {
            return null;
        }
        String productName = product.getProductName();
        if ( productName == null ) {
            return null;
        }
        return productName;
    }
}
