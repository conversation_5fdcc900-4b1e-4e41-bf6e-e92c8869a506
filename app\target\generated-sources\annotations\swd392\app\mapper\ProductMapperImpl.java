package swd392.app.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import swd392.app.dto.request.ProductRequest;
import swd392.app.dto.response.ProductResponse;
import swd392.app.entity.Product;
import swd392.app.entity.ProductType;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class ProductMapperImpl implements ProductMapper {

    @Override
    public Product toEntity(ProductRequest request) {
        if ( request == null ) {
            return null;
        }

        Product.ProductBuilder product = Product.builder();

        product.productCode( request.getProductCode() );
        product.productName( request.getProductName() );
        product.size( request.getSize() );
        product.color( request.getColor() );
        product.quantity( request.getQuantity() );

        return product.build();
    }

    @Override
    public ProductResponse toResponse(Product product) {
        if ( product == null ) {
            return null;
        }

        ProductResponse.ProductResponseBuilder productResponse = ProductResponse.builder();

        productResponse.productTypeName( productProductTypeProductTypeName( product ) );
        productResponse.productCode( product.getProductCode() );
        productResponse.productName( product.getProductName() );
        productResponse.size( product.getSize() );
        productResponse.color( product.getColor() );
        productResponse.quantity( product.getQuantity() );

        return productResponse.build();
    }

    private String productProductTypeProductTypeName(Product product) {
        if ( product == null ) {
            return null;
        }
        ProductType productType = product.getProductType();
        if ( productType == null ) {
            return null;
        }
        String productTypeName = productType.getProductTypeName();
        if ( productTypeName == null ) {
            return null;
        }
        return productTypeName;
    }
}
