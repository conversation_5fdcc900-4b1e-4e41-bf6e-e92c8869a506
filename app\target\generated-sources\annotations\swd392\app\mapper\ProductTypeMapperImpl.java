package swd392.app.mapper;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;
import swd392.app.dto.request.ProductTypeRequest;
import swd392.app.dto.response.ProductTypeResponse;
import swd392.app.entity.Category;
import swd392.app.entity.ProductType;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class ProductTypeMapperImpl implements ProductTypeMapper {

    @Override
    public ProductType toEntity(ProductTypeRequest request) {
        if ( request == null ) {
            return null;
        }

        ProductType.ProductTypeBuilder productType = ProductType.builder();

        productType.productTypeCode( request.getProductTypeCode() );
        productType.productTypeName( request.getProductTypeName() );
        productType.price( request.getPrice() );

        return productType.build();
    }

    @Override
    public ProductTypeResponse toResponse(ProductType productType) {
        if ( productType == null ) {
            return null;
        }

        ProductTypeResponse.ProductTypeResponseBuilder productTypeResponse = ProductTypeResponse.builder();

        productTypeResponse.categoryName( productTypeCategoryCategoryName( productType ) );
        productTypeResponse.productTypeCode( productType.getProductTypeCode() );
        productTypeResponse.productTypeName( productType.getProductTypeName() );
        productTypeResponse.price( productType.getPrice() );

        return productTypeResponse.build();
    }

    private String productTypeCategoryCategoryName(ProductType productType) {
        if ( productType == null ) {
            return null;
        }
        Category category = productType.getCategory();
        if ( category == null ) {
            return null;
        }
        String categoryName = category.getCategoryName();
        if ( categoryName == null ) {
            return null;
        }
        return categoryName;
    }
}
